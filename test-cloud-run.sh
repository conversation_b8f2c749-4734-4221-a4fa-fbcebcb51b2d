#!/bin/bash

# Test script for Cloud Run MCP Rules Engine
# Usage: ./test-cloud-run.sh [staging|production]

ENVIRONMENT=${1:-staging}

if [ "$ENVIRONMENT" = "staging" ]; then
    SERVICE_URL="https://mcp-staging-122290401475.us-central1.run.app"
elif [ "$ENVIRONMENT" = "production" ]; then
    SERVICE_URL="https://mcp-prod-122290401475.us-central1.run.app"
else
    echo "Usage: $0 [staging|production]"
    exit 1
fi

echo "Testing MCP Rules Engine - $ENVIRONMENT environment"
echo "Service URL: $SERVICE_URL"
echo ""

# Get access token
echo "Getting access token..."
ACCESS_TOKEN=$(gcloud auth print-access-token)

if [ -z "$ACCESS_TOKEN" ]; then
    echo "Error: Could not get access token. Please run 'gcloud auth login'"
    exit 1
fi

echo "✓ Access token obtained"
echo ""

# Test health endpoint
echo "Testing /health endpoint..."
HEALTH_RESPONSE=$(curl -s -H "Authorization: Bearer $ACCESS_TOKEN" "$SERVICE_URL/health")
echo "Response: $HEALTH_RESPONSE"
echo ""

# Test MCP endpoint with calculate_deadlines
echo "Testing /mcp/run endpoint with calculate_deadlines..."
MCP_RESPONSE=$(curl -s -H "Authorization: Bearer $ACCESS_TOKEN" \
    -H "Content-Type: application/json" \
    -X POST "$SERVICE_URL/mcp/run" \
    -d '{
        "toolName": "calculate_deadlines",
        "params": {
            "jurisdiction": "TX_STATE",
            "triggerCode": "ARREST_BOOKING",
            "startDate": "2025-01-15"
        }
    }')
echo "Response: $MCP_RESPONSE"
echo ""

# Test MCP endpoint with list_jurisdictions
echo "Testing /mcp/run endpoint with list_jurisdictions..."
JURISDICTIONS_RESPONSE=$(curl -s -H "Authorization: Bearer $ACCESS_TOKEN" \
    -H "Content-Type: application/json" \
    -X POST "$SERVICE_URL/mcp/run" \
    -d '{
        "toolName": "list_jurisdictions",
        "params": {}
    }')
echo "Response: $JURISDICTIONS_RESPONSE"
echo ""

echo "Testing complete!"
