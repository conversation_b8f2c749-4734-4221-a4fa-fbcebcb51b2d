#!/bin/bash

# MCP Rules Engine - Google Cloud Run Deployment Script
# Usage: ./cloud-run-deploy.sh [staging|production]

set -e

# Configuration
PROJECT_ID="texas-laws-personalinjury"  # Update with your actual project ID
REGION="us-central1"
STAGING_SERVICE="mcp-staging"
PRODUCTION_SERVICE="mcp-prod"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if environment argument is provided
if [ $# -eq 0 ]; then
    print_error "Please specify environment: staging or production"
    echo "Usage: $0 [staging|production]"
    exit 1
fi

ENVIRONMENT=$1

# Validate environment
if [ "$ENVIRONMENT" != "staging" ] && [ "$ENVIRONMENT" != "production" ]; then
    print_error "Invalid environment. Use 'staging' or 'production'"
    exit 1
fi

# Set service name based on environment
if [ "$ENVIRONMENT" = "staging" ]; then
    SERVICE_NAME=$STAGING_SERVICE
    ENV_SUFFIX="staging"
else
    SERVICE_NAME=$PRODUCTION_SERVICE
    ENV_SUFFIX="prod"
fi

print_status "Deploying MCP Rules Engine to $ENVIRONMENT environment..."
print_status "Service: $SERVICE_NAME"
print_status "Region: $REGION"

# Check if gcloud is installed and authenticated
if ! command -v gcloud &> /dev/null; then
    print_error "gcloud CLI is not installed. Please install it first."
    exit 1
fi

# Check if user is authenticated
if ! gcloud auth list --filter=status:ACTIVE --format="value(account)" | grep -q .; then
    print_error "Not authenticated with gcloud. Please run 'gcloud auth login'"
    exit 1
fi

# Set the project
print_status "Setting project to $PROJECT_ID..."
gcloud config set project $PROJECT_ID

# Enable required APIs
print_status "Enabling required APIs..."
gcloud services enable cloudbuild.googleapis.com
gcloud services enable run.googleapis.com

# Build and deploy
print_status "Building and deploying to Cloud Run..."

# Deploy with environment-specific configuration
gcloud run deploy $SERVICE_NAME \
    --source=. \
    --region=$REGION \
    --platform=managed \
    --allow-unauthenticated \
    --port=4000 \
    --memory=1Gi \
    --cpu=1 \
    --min-instances=0 \
    --max-instances=10 \
    --timeout=300 \
    --concurrency=80 \
    --set-env-vars="NODE_ENV=$ENVIRONMENT,PORT=4000" \
    --tag=$ENV_SUFFIX

if [ $? -eq 0 ]; then
    print_status "Deployment successful!"
    
    # Get the service URL
    SERVICE_URL=$(gcloud run services describe $SERVICE_NAME --region=$REGION --format="value(status.url)")
    
    print_status "Service URL: $SERVICE_URL"
    print_status "Health check: $SERVICE_URL/health"
    print_status "MCP endpoint: $SERVICE_URL/mcp/run"
    
    # Test the health endpoint
    print_status "Testing health endpoint..."
    if curl -f "$SERVICE_URL/health" > /dev/null 2>&1; then
        print_status "Health check passed!"
    else
        print_warning "Health check failed. Please check the logs."
    fi
    
    # Show logs command
    print_status "To view logs, run:"
    echo "gcloud run services logs read $SERVICE_NAME --region=$REGION --limit=50"
    
else
    print_error "Deployment failed!"
    exit 1
fi
