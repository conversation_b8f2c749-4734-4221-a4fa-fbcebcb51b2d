# 🚀 MCP Rules Engine - Cloud Run Deployment Plan

## 📋 **DEPLOYMENT OVERVIEW**

**Objective**: Deploy production-ready MCP Rules Engine with 1,577+ deadline rules to Google Cloud Run

**Timeline**: 1-2 weeks for staging + production deployment

**Architecture**: Dual environment (staging/production) with automated testing

---

## 🏗️ **DEPLOYMENT ARCHITECTURE**

```
┌─────────────────────────────────────────────────────────────┐
│                    Google Cloud Run                        │
├─────────────────────────────┬───────────────────────────────┤
│         STAGING             │         PRODUCTION            │
│    mcp-staging              │         mcp-prod              │
│    us-central1              │         us-central1           │
│                             │                               │
│  • 0-10 instances           │  • 1-20 instances             │
│  • Scale to zero            │  • Always warm                │
│  • Debug logging           │  • Production logging         │
│  • Test environment        │  • Live environment           │
└─────────────────────────────┴───────────────────────────────┘
                              │
                    ┌─────────────────┐
                    │  Load Balancer  │
                    │   (Optional)    │
                    └─────────────────┘
```

---

## 📦 **CURRENT SYSTEM STATUS**

✅ **Ready for Deployment**:
- **82 YAML rule files** with legal deadline rules
- **Express.js server** with MCP endpoint (`/mcp/run`)
- **Health check endpoint** (`/health`)
- **Docker<PERSON>le** optimized for Cloud Run
- **TypeScript build system** with production compilation

✅ **Key Features**:
- **Jurisdiction-based rule loading** (FL_STATE, TX_STATE, etc.)
- **Holiday calculation support** with caching
- **Error handling** with proper HTTP status codes
- **Input validation** using Zod schemas

---

## 🛠️ **DEPLOYMENT FILES CREATED**

### Core Deployment Scripts
- `deployment/cloud-run-deploy.sh` - Main deployment script
- `deployment/test-deployment.sh` - Comprehensive testing script
- `deployment/setup.sh` - Initial environment setup

### Configuration Files
- `deployment/cloud-run-config.yaml` - Service configuration template
- `deployment/staging.env` - Staging environment variables
- `deployment/production.env` - Production environment variables
- `.gcloudignore` - Optimized build exclusions

### CI/CD Pipeline
- `.github/workflows/deploy-cloud-run.yml` - Automated deployment workflow

### Documentation
- `deployment/README.md` - Comprehensive deployment guide

---

## 🚀 **IMMEDIATE NEXT STEPS**

### **Phase 1: Initial Setup (Day 1)**
```bash
# 1. Run initial setup
./deployment/setup.sh

# 2. Deploy to staging
./deployment/cloud-run-deploy.sh staging

# 3. Test staging deployment
./deployment/test-deployment.sh staging [STAGING_URL]
```

### **Phase 2: Production Deployment (Day 2-3)**
```bash
# 1. Deploy to production
./deployment/cloud-run-deploy.sh production

# 2. Test production deployment
./deployment/test-deployment.sh production [PRODUCTION_URL]

# 3. Verify all 1,577 rules are accessible
```

### **Phase 3: Integration & Testing (Week 1)**
- **MCP Integration Testing**: Verify all jurisdiction rules load correctly
- **Performance Testing**: Test with realistic workloads
- **Error Handling**: Validate edge cases and error responses
- **Documentation**: Update API documentation with live endpoints

---

## 🔧 **TECHNICAL SPECIFICATIONS**

### **Resource Allocation**
- **Memory**: 1Gi (sufficient for rule loading and caching)
- **CPU**: 1 vCPU (handles concurrent requests efficiently)
- **Concurrency**: 80 requests per instance
- **Timeout**: 300 seconds (accommodates rule loading)

### **Scaling Configuration**
| Environment | Min Instances | Max Instances | Scale to Zero |
|-------------|---------------|---------------|---------------|
| Staging     | 0             | 10            | ✅ Yes        |
| Production  | 1             | 20            | ❌ No         |

### **Environment Variables**
- `NODE_ENV`: staging/production
- `PORT`: 4000
- `LOG_LEVEL`: debug/info
- `GEMINI_API_KEY`: (via Secret Manager)

---

## 🧪 **TESTING STRATEGY**

### **Automated Tests Include**:
1. **Health Check**: Service availability
2. **MCP Endpoint**: Deadline calculation functionality
3. **Error Handling**: Invalid request validation
4. **Load Testing**: Concurrent request handling
5. **Performance**: Response time validation

### **Test Jurisdictions**:
- `FL_STATE`: Florida Rules (187 rules)
- `TX_STATE`: Texas Rules (comprehensive set)
- Error cases: Invalid jurisdictions and trigger codes

---

## 💰 **COST ESTIMATION**

### **Staging Environment**
- **Base cost**: ~$0-5/month (scales to zero)
- **Usage-based**: Pay only when testing

### **Production Environment**
- **Base cost**: ~$10-30/month (1 warm instance)
- **Scale cost**: Additional instances as needed
- **Total estimated**: $15-50/month depending on usage

---

## 🔒 **SECURITY & MONITORING**

### **Security Features**
- **Unauthenticated access** (as per current design)
- **Input validation** with Zod schemas
- **Error sanitization** (no sensitive data exposure)

### **Monitoring Setup**
- **Health checks**: Built-in liveness/readiness probes
- **Logging**: Structured JSON logs to Cloud Logging
- **Metrics**: Request count, latency, error rates

---

## 📈 **SUCCESS METRICS**

### **Deployment Success Criteria**
- ✅ Both environments deployed and accessible
- ✅ All 1,577 rules loadable via MCP
- ✅ Response time < 1 second for cached rules
- ✅ 99.9% uptime for production environment
- ✅ Automated testing pipeline functional

### **Performance Targets**
- **Cold start**: < 10 seconds
- **Warm response**: < 500ms
- **Rule loading**: < 2 seconds per jurisdiction
- **Concurrent requests**: 80+ per instance

---

## 🔄 **FUTURE ENHANCEMENTS**

### **Phase 2 Improvements (Weeks 3-4)**
1. **Load Balancer**: Add Cloud Load Balancing for production
2. **CDN**: Implement caching for static rule data
3. **Database**: Consider persistent storage for rule caching
4. **Authentication**: Add API key authentication if needed
5. **Rate Limiting**: Implement request throttling

### **Monitoring & Observability**
1. **Cloud Monitoring**: Set up alerts and dashboards
2. **Error Tracking**: Implement error aggregation
3. **Performance Monitoring**: Track response times and throughput

---

## 📞 **SUPPORT & TROUBLESHOOTING**

### **Common Commands**
```bash
# View logs
gcloud run services logs read mcp-staging --region=us-central1

# Update service
gcloud run services update mcp-prod --region=us-central1

# Check service status
gcloud run services describe mcp-prod --region=us-central1
```

### **Emergency Procedures**
1. **Rollback**: Use previous revision tags
2. **Scale down**: Reduce max instances if needed
3. **Debug**: Enable detailed logging in staging

---

## ✅ **READY TO DEPLOY**

The MCP Rules Engine is **production-ready** with:
- ✅ Complete deployment automation
- ✅ Comprehensive testing suite
- ✅ Dual environment setup
- ✅ Monitoring and logging
- ✅ Cost-optimized configuration

**Next Action**: Run `./deployment/setup.sh` to begin deployment process.
